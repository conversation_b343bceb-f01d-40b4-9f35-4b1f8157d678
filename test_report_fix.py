#!/usr/bin/env python3
"""
Test script to regenerate the HTML report with screenshot links fixed
"""

import os
import sys
import json
import shutil
from jinja2 import Template

# Add the app directory to the path
sys.path.append('app')

def regenerate_report_with_fixes():
    """Regenerate the HTML report with the fixes applied"""
    
    # Paths
    export_dir = "reports/export_testsuite_execution_20250616_210919_20250616_211032"
    template_path = "app/templates/custom_report_template.html"

    # Create test data based on what we can see from the HTML report
    test_data = {
        "testCases": [
            {
                "name": "health2 - testing labels",
                "status": "passed",
                "steps": [
                    {"name": "Launch app: com.apple.Health", "status": "passed", "action_id": "UppP3ZuqY6"},
                    {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "passed", "action_id": "Successful"},
                    {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "action_id": "XCUIElemen"},
                    {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "passed", "action_id": "screenshot"},
                    {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "action_id": "XCUIElemen"},
                    {"name": "Add Log: Done link is clicked (with screenshot)", "status": "passed", "action_id": "screenshot"},
                    {"name": "Wait for 1 ms", "status": "passed", "action_id": "ag29wsBP24"},
                    {"name": "Terminate app: com.apple.Health", "status": "passed", "action_id": "vjBGuN5y9x"},
                    {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "action_id": "screenshot"}
                ]
            },
            {
                "name": "apple health",
                "status": "passed",
                "steps": [
                    {"name": "Launch app: com.apple.Health", "status": "passed", "action_id": "UppP3ZuqY6"},
                    {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "action_id": "XCUIElemen"},
                    {"name": "takeScreenshot action", "status": "passed", "action_id": "takeScreen"},
                    {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "action_id": "XCUIElemen"},
                    {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "action_id": "XCUIElemen"},
                    {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "passed", "action_id": "successful"},
                    {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "action_id": "XCUIElemen"},
                    {"name": "Terminate app: com.apple.Health", "status": "passed", "action_id": "ag29wsBP24"},
                    {"name": "takeScreenshot action", "status": "passed", "action_id": "takeScreen"},
                    {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "action_id": "screenshot"}
                ]
            }
        ]
    }
    
    # Check if template exists
    if not os.path.exists(template_path):
        print(f"Template not found at: {template_path}")
        return False
    
    print(f"Loaded test data with {len(test_data.get('testCases', []))} test cases")
    
    # Process the test data to add screenshot links for takeScreenshot actions
    processed_test_cases = []
    
    for tc in test_data.get('testCases', []):
        processed_tc = {
            'name': tc.get('name', ''),
            'status': tc.get('status', 'unknown').title(),
            'actions': []
        }
        
        for step in tc.get('steps', []):
            action = {
                'type': step.get('name', '').split(':')[0].strip() if ':' in step.get('name', '') else 'action',
                'description': step.get('name', ''),
                'status': step.get('status', 'unknown')
            }
            
            # Check if this is a takeScreenshot action and add screenshot link
            action_id = step.get('action_id', '')
            step_name = step.get('name', '').lower()
            
            if action_id and ('takescreenshot' in step_name):
                # Set the screenshot path for takeScreenshot actions
                screenshot_path = f"screenshots/{action_id}.png"
                action['screenshot'] = screenshot_path
                print(f"Added screenshot for takeScreenshot action: {screenshot_path}")
            
            processed_tc['actions'].append(action)
        
        processed_test_cases.append(processed_tc)
    
    # Load the template
    with open(template_path, 'r') as f:
        template_content = f.read()
    
    template = Template(template_content)
    
    # Prepare template context
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    context = {
        'timestamp': timestamp,
        'total_test_cases': len(processed_test_cases),
        'passed_test_cases': sum(1 for tc in processed_test_cases if tc.get('status') == 'Passed'),
        'failed_test_cases': sum(1 for tc in processed_test_cases if tc.get('status') == 'Failed'),
        'test_cases': processed_test_cases
    }
    
    # Render the template
    html_content = template.render(**context)
    
    # Write the new HTML report
    output_path = os.path.join(export_dir, "test_execution_report_fixed.html")
    with open(output_path, 'w') as f:
        f.write(html_content)
    
    print(f"Fixed HTML report generated at: {output_path}")
    return True

if __name__ == "__main__":
    success = regenerate_report_with_fixes()
    if success:
        print("Report regeneration completed successfully!")
    else:
        print("Report regeneration failed!")
