Action Log - 2025-06-16 21:22:19
================================================================================

[[21:22:18]] [INFO] Generating execution report...
[[21:22:18]] [SUCCESS] All tests passed successfully!
[[21:22:18]] [SUCCESS] Screenshot refreshed
[[21:22:18]] [INFO] Refreshing screenshot...
[[21:22:16]] [SUCCESS] Screenshot refreshed successfully
[[21:22:16]] [SUCCESS] Screenshot refreshed successfully
[[21:22:16]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[21:22:15]] [SUCCESS] Screenshot refreshed
[[21:22:15]] [INFO] Refreshing screenshot...
[[21:22:13]] [SUCCESS] Screenshot refreshed successfully
[[21:22:13]] [SUCCESS] Screenshot refreshed successfully
[[21:22:12]] [INFO] Executing action 18/19: takeScreenshot action
[[21:22:11]] [SUCCESS] Screenshot refreshed
[[21:22:11]] [INFO] Refreshing screenshot...
[[21:22:08]] [SUCCESS] Screenshot refreshed successfully
[[21:22:08]] [SUCCESS] Screenshot refreshed successfully
[[21:22:08]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[21:22:08]] [SUCCESS] Screenshot refreshed
[[21:22:08]] [INFO] Refreshing screenshot...
[[21:22:05]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:22:05]] [SUCCESS] Screenshot refreshed successfully
[[21:22:05]] [SUCCESS] Screenshot refreshed successfully
[[21:22:04]] [SUCCESS] Screenshot refreshed
[[21:22:04]] [INFO] Refreshing screenshot...
[[21:22:03]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:22:03]] [SUCCESS] Screenshot refreshed successfully
[[21:22:03]] [SUCCESS] Screenshot refreshed successfully
[[21:22:02]] [SUCCESS] Screenshot refreshed
[[21:22:02]] [INFO] Refreshing screenshot...
[[21:22:00]] [SUCCESS] Screenshot refreshed successfully
[[21:22:00]] [SUCCESS] Screenshot refreshed successfully
[[21:22:00]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:21:59]] [SUCCESS] Screenshot refreshed
[[21:21:59]] [INFO] Refreshing screenshot...
[[21:21:56]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:21:56]] [SUCCESS] Screenshot refreshed successfully
[[21:21:56]] [SUCCESS] Screenshot refreshed successfully
[[21:21:55]] [SUCCESS] Screenshot refreshed
[[21:21:55]] [INFO] Refreshing screenshot...
[[21:21:53]] [INFO] Executing action 12/19: takeScreenshot action
[[21:21:53]] [SUCCESS] Screenshot refreshed successfully
[[21:21:53]] [SUCCESS] Screenshot refreshed successfully
[[21:21:53]] [SUCCESS] Screenshot refreshed
[[21:21:53]] [INFO] Refreshing screenshot...
[[21:21:50]] [SUCCESS] Screenshot refreshed successfully
[[21:21:50]] [SUCCESS] Screenshot refreshed successfully
[[21:21:50]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:21:50]] [SUCCESS] Screenshot refreshed
[[21:21:50]] [INFO] Refreshing screenshot...
[[21:21:38]] [SUCCESS] Screenshot refreshed successfully
[[21:21:38]] [SUCCESS] Screenshot refreshed successfully
[[21:21:37]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[21:21:37]] [SUCCESS] Screenshot refreshed
[[21:21:37]] [INFO] Refreshing screenshot...
[[21:21:35]] [SUCCESS] Screenshot refreshed successfully
[[21:21:35]] [SUCCESS] Screenshot refreshed successfully
[[21:21:34]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[21:21:34]] [SUCCESS] Screenshot refreshed
[[21:21:34]] [INFO] Refreshing screenshot...
[[21:21:31]] [SUCCESS] Screenshot refreshed successfully
[[21:21:31]] [SUCCESS] Screenshot refreshed successfully
[[21:21:31]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[21:21:30]] [SUCCESS] Screenshot refreshed
[[21:21:30]] [INFO] Refreshing screenshot...
[[21:21:28]] [SUCCESS] Screenshot refreshed successfully
[[21:21:28]] [SUCCESS] Screenshot refreshed successfully
[[21:21:28]] [INFO] Executing action 7/19: Wait for 1 ms
[[21:21:27]] [SUCCESS] Screenshot refreshed
[[21:21:27]] [INFO] Refreshing screenshot...
[[21:21:26]] [SUCCESS] Screenshot refreshed successfully
[[21:21:26]] [SUCCESS] Screenshot refreshed successfully
[[21:21:26]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[21:21:25]] [SUCCESS] Screenshot refreshed
[[21:21:25]] [INFO] Refreshing screenshot...
[[21:21:22]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:21:22]] [SUCCESS] Screenshot refreshed successfully
[[21:21:22]] [SUCCESS] Screenshot refreshed successfully
[[21:21:21]] [SUCCESS] Screenshot refreshed
[[21:21:21]] [INFO] Refreshing screenshot...
[[21:21:20]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[21:21:20]] [SUCCESS] Screenshot refreshed successfully
[[21:21:20]] [SUCCESS] Screenshot refreshed successfully
[[21:21:19]] [SUCCESS] Screenshot refreshed
[[21:21:19]] [INFO] Refreshing screenshot...
[[21:21:17]] [SUCCESS] Screenshot refreshed successfully
[[21:21:17]] [SUCCESS] Screenshot refreshed successfully
[[21:21:17]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:21:16]] [SUCCESS] Screenshot refreshed
[[21:21:16]] [INFO] Refreshing screenshot...
[[21:21:15]] [SUCCESS] Screenshot refreshed successfully
[[21:21:15]] [SUCCESS] Screenshot refreshed successfully
[[21:21:15]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[21:21:14]] [SUCCESS] Screenshot refreshed
[[21:21:14]] [INFO] Refreshing screenshot...
[[21:21:11]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[21:21:11]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[21:21:11]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[21:21:11]] [INFO] Clearing screenshots from database before execution...
[[21:21:11]] [SUCCESS] All screenshots deleted successfully
[[21:21:11]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:21:11]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_212111/screenshots
[[21:21:11]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_212111
[[21:21:11]] [SUCCESS] Report directory initialized successfully
[[21:21:11]] [INFO] Initializing report directory and screenshots folder...
[[21:21:10]] [SUCCESS] All screenshots deleted successfully
[[21:21:10]] [INFO] All actions cleared
[[21:21:10]] [INFO] Cleaning up screenshots...
[[21:21:04]] [SUCCESS] Screenshot refreshed successfully
[[21:21:03]] [SUCCESS] Screenshot refreshed
[[21:21:03]] [INFO] Refreshing screenshot...
[[21:21:02]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:21:02]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:20:59]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:20:57]] [SUCCESS] Found 1 device(s)
[[21:20:56]] [INFO] Refreshing device list...
