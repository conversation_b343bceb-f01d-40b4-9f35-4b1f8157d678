Action Log - 2025-06-16 21:53:40
================================================================================

[[21:53:40]] [INFO] Generating execution report...
[[21:53:40]] [SUCCESS] All tests passed successfully!
[[21:53:39]] [SUCCESS] Screenshot refreshed
[[21:53:39]] [INFO] Refreshing screenshot...
[[21:53:38]] [SUCCESS] Screenshot refreshed successfully
[[21:53:38]] [SUCCESS] Screenshot refreshed successfully
[[21:53:37]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[21:53:37]] [SUCCESS] Screenshot refreshed
[[21:53:37]] [INFO] Refreshing screenshot...
[[21:53:34]] [SUCCESS] Screenshot refreshed successfully
[[21:53:34]] [SUCCESS] Screenshot refreshed successfully
[[21:53:33]] [INFO] Executing action 18/19: takeScreenshot action
[[21:53:33]] [SUCCESS] Screenshot refreshed
[[21:53:33]] [INFO] Refreshing screenshot...
[[21:53:30]] [SUCCESS] Screenshot refreshed successfully
[[21:53:30]] [SUCCESS] Screenshot refreshed successfully
[[21:53:30]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[21:53:29]] [SUCCESS] Screenshot refreshed
[[21:53:29]] [INFO] Refreshing screenshot...
[[21:53:26]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:53:26]] [SUCCESS] Screenshot refreshed successfully
[[21:53:26]] [SUCCESS] Screenshot refreshed successfully
[[21:53:26]] [SUCCESS] Screenshot refreshed
[[21:53:26]] [INFO] Refreshing screenshot...
[[21:53:24]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:53:24]] [SUCCESS] Screenshot refreshed successfully
[[21:53:24]] [SUCCESS] Screenshot refreshed successfully
[[21:53:24]] [SUCCESS] Screenshot refreshed
[[21:53:24]] [INFO] Refreshing screenshot...
[[21:53:21]] [SUCCESS] Screenshot refreshed successfully
[[21:53:21]] [SUCCESS] Screenshot refreshed successfully
[[21:53:21]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:53:20]] [SUCCESS] Screenshot refreshed
[[21:53:20]] [INFO] Refreshing screenshot...
[[21:53:17]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:53:17]] [SUCCESS] Screenshot refreshed successfully
[[21:53:17]] [SUCCESS] Screenshot refreshed successfully
[[21:53:17]] [SUCCESS] Screenshot refreshed
[[21:53:17]] [INFO] Refreshing screenshot...
[[21:53:15]] [INFO] Executing action 12/19: takeScreenshot action
[[21:53:15]] [SUCCESS] Screenshot refreshed successfully
[[21:53:15]] [SUCCESS] Screenshot refreshed successfully
[[21:53:14]] [SUCCESS] Screenshot refreshed
[[21:53:14]] [INFO] Refreshing screenshot...
[[21:53:12]] [SUCCESS] Screenshot refreshed successfully
[[21:53:12]] [SUCCESS] Screenshot refreshed successfully
[[21:53:12]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:53:11]] [SUCCESS] Screenshot refreshed
[[21:53:11]] [INFO] Refreshing screenshot...
[[21:52:59]] [SUCCESS] Screenshot refreshed successfully
[[21:52:59]] [SUCCESS] Screenshot refreshed successfully
[[21:52:58]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[21:52:58]] [SUCCESS] Screenshot refreshed
[[21:52:58]] [INFO] Refreshing screenshot...
[[21:52:56]] [SUCCESS] Screenshot refreshed successfully
[[21:52:56]] [SUCCESS] Screenshot refreshed successfully
[[21:52:56]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[21:52:55]] [SUCCESS] Screenshot refreshed
[[21:52:55]] [INFO] Refreshing screenshot...
[[21:52:52]] [SUCCESS] Screenshot refreshed successfully
[[21:52:52]] [SUCCESS] Screenshot refreshed successfully
[[21:52:52]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[21:52:52]] [SUCCESS] Screenshot refreshed
[[21:52:52]] [INFO] Refreshing screenshot...
[[21:52:49]] [SUCCESS] Screenshot refreshed successfully
[[21:52:49]] [SUCCESS] Screenshot refreshed successfully
[[21:52:49]] [INFO] Executing action 7/19: Wait for 1 ms
[[21:52:48]] [SUCCESS] Screenshot refreshed
[[21:52:48]] [INFO] Refreshing screenshot...
[[21:52:47]] [SUCCESS] Screenshot refreshed successfully
[[21:52:47]] [SUCCESS] Screenshot refreshed successfully
[[21:52:47]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[21:52:46]] [SUCCESS] Screenshot refreshed
[[21:52:46]] [INFO] Refreshing screenshot...
[[21:52:43]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:52:43]] [SUCCESS] Screenshot refreshed successfully
[[21:52:43]] [SUCCESS] Screenshot refreshed successfully
[[21:52:43]] [SUCCESS] Screenshot refreshed
[[21:52:43]] [INFO] Refreshing screenshot...
[[21:52:41]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[21:52:41]] [SUCCESS] Screenshot refreshed successfully
[[21:52:41]] [SUCCESS] Screenshot refreshed successfully
[[21:52:41]] [SUCCESS] Screenshot refreshed
[[21:52:41]] [INFO] Refreshing screenshot...
[[21:52:38]] [SUCCESS] Screenshot refreshed successfully
[[21:52:38]] [SUCCESS] Screenshot refreshed successfully
[[21:52:38]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:52:38]] [SUCCESS] Screenshot refreshed
[[21:52:38]] [INFO] Refreshing screenshot...
[[21:52:36]] [SUCCESS] Screenshot refreshed successfully
[[21:52:36]] [SUCCESS] Screenshot refreshed successfully
[[21:52:36]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[21:52:35]] [SUCCESS] Screenshot refreshed
[[21:52:35]] [INFO] Refreshing screenshot...
[[21:52:32]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[21:52:32]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[21:52:32]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[21:52:32]] [INFO] Clearing screenshots from database before execution...
[[21:52:32]] [SUCCESS] All screenshots deleted successfully
[[21:52:32]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:52:32]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_215232/screenshots
[[21:52:32]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_215232
[[21:52:32]] [SUCCESS] Report directory initialized successfully
[[21:52:32]] [INFO] Initializing report directory and screenshots folder...
[[21:52:30]] [SUCCESS] All screenshots deleted successfully
[[21:52:30]] [INFO] All actions cleared
[[21:52:30]] [INFO] Cleaning up screenshots...
[[21:51:50]] [SUCCESS] Screenshot refreshed successfully
[[21:51:49]] [SUCCESS] Screenshot refreshed
[[21:51:49]] [INFO] Refreshing screenshot...
[[21:51:48]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:51:48]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:51:45]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:51:43]] [SUCCESS] Found 1 device(s)
[[21:51:42]] [INFO] Refreshing device list...
