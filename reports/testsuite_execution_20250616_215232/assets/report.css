/* Test Report Styling */

/* General Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    color: #333;
    background-color: #f5f5f7;
    margin: 0;
    padding: 0;
}

/* Header Styles */
.header {
    background-color: #fff;
    padding: 20px;
    border-bottom: 1px solid #e1e4e8;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
}

.status-summary {
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
    text-transform: uppercase;
}

.status-badge-passed {
    background-color: #28a745;
    color: white;
}

.status-badge-failed {
    background-color: #dc3545;
    color: white;
}

.status-badge-skipped {
    background-color: #ffc107;
    color: #212529;
}

.passed-count {
    color: #28a745;
    font-weight: 600;
}

.failed-count {
    color: #dc3545;
    font-weight: 600;
}

.skipped-count {
    color: #ffc107;
    font-weight: 600;
}

/* Content Layout */
.content {
    display: flex;
    min-height: calc(100vh - 80px);
}

.suites-panel {
    flex: 0 0 60%;
    background-color: #fff;
    border-right: 1px solid #e1e4e8;
    overflow-y: auto;
}

.details-panel {
    flex: 1;
    padding: 20px;
    background-color: #f8f9fa;
    overflow-y: auto;
}

/* Suite Styles */
.suite-heading {
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    background-color: #f1f3f5;
    border-bottom: 1px solid #e1e4e8;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.expand-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23333' d='M6 12l4-4-4-4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s;
}

.expanded .expand-icon {
    transform: rotate(90deg);
}

/* Test List Styles */
.test-list {
    list-style: none;
}

.test-item {
    border-bottom: 1px solid #e1e4e8;
}

.test-header {
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.test-case {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 1.4;
    flex-wrap: wrap;
}

.test-case:after {
    content: attr(data-actions);
    display: block;
    width: 100%;
    margin-top: 4px;
    font-size: 12px;
    color: #555;
    padding-left: 20px;
}

.test-duration {
    font-size: 12px;
    color: #6c757d;
}

.status-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 50%;
}

.status-icon-passed {
    background-color: #28a745;
}

.status-icon-failed {
    background-color: #dc3545;
}

.status-icon-skipped {
    background-color: #ffc107;
}

/* Test Steps Styles */
.test-steps {
    list-style: none;
    background-color: #f8f9fa;
    display: none;
}

.test-item.expanded .test-steps {
    display: block;
}

.test-step {
    padding: 8px 20px 8px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s;
}

.test-step:hover {
    background-color: #e9ecef;
}

.test-step.active {
    background-color: #e2e6ea;
    border-left: 3px solid #007bff;
}

.test-step-name {
    display: flex;
    align-items: flex-start;
    font-size: 13px;
    overflow-wrap: break-word;
    word-break: break-word;
    width: 80%;
}

.test-step-duration {
    font-size: 12px;
    color: #6c757d;
    flex-shrink: 0;
    text-align: right;
}

/* Action ID Badge */
.action-id-badge {
    display: inline-block;
    font-size: 10px;
    background-color: #e9ecef;
    color: #495057;
    padding: 1px 4px;
    border-radius: 3px;
    margin-left: 5px;
    font-family: monospace;
    vertical-align: middle;
}

/* Step Details */
.step-details {
    margin: 20px;
    padding: 15px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.step-details h3 {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
}

.screenshot-container {
    margin: 15px 0;
    text-align: center;
}

.action-id {
    font-size: 13px;
    color: #333;
    margin-bottom: 8px;
    font-family: monospace;
    background-color: #f8f9fa;
    padding: 3px 6px;
    border-radius: 3px;
    display: inline-block;
}

.action-id-value {
    font-weight: bold;
    color: #007bff;
}

.screenshot-container img {
    max-width: 50%;  /* Reduced from 100% to 50% */
    max-height: 400px; /* Added max height */
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    object-fit: contain; /* Maintain aspect ratio */
}

/* Error Details */
.error-details {
    margin-top: 15px;
    border-top: 1px solid #f5c6cb;
    padding-top: 15px;
}

.error-details h4 {
    color: #dc3545;
    margin-bottom: 10px;
    font-size: 16px;
}

.error-details pre {
    background-color: #f8f9fa;
    border: 1px solid #eaecef;
    border-radius: 4px;
    padding: 10px;
    overflow-x: auto;
    font-size: 13px;
    color: #e83e8c;
}

/* Responsive adjustments */
@media screen and (max-width: 992px) {
    .content {
        flex-direction: column;
    }

    .suites-panel {
        flex: 0 0 auto;
        border-right: none;
        border-bottom: 1px solid #e1e4e8;
    }
}