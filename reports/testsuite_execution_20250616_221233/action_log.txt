Action Log - 2025-06-16 22:13:41
================================================================================

[[22:13:41]] [INFO] Generating execution report...
[[22:13:41]] [SUCCESS] All tests passed successfully!
[[22:13:40]] [SUCCESS] Screenshot refreshed
[[22:13:40]] [INFO] Refreshing screenshot...
[[22:13:39]] [SUCCESS] Screenshot refreshed successfully
[[22:13:39]] [SUCCESS] Screenshot refreshed successfully
[[22:13:38]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[22:13:38]] [SUCCESS] Screenshot refreshed
[[22:13:38]] [INFO] Refreshing screenshot...
[[22:13:35]] [SUCCESS] Screenshot refreshed successfully
[[22:13:35]] [SUCCESS] Screenshot refreshed successfully
[[22:13:35]] [INFO] Executing action 18/19: takeScreenshot action
[[22:13:34]] [SUCCESS] Screenshot refreshed
[[22:13:34]] [INFO] Refreshing screenshot...
[[22:13:31]] [SUCCESS] Screenshot refreshed successfully
[[22:13:31]] [SUCCESS] Screenshot refreshed successfully
[[22:13:31]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[22:13:30]] [SUCCESS] Screenshot refreshed
[[22:13:30]] [INFO] Refreshing screenshot...
[[22:13:27]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[22:13:27]] [SUCCESS] Screenshot refreshed successfully
[[22:13:27]] [SUCCESS] Screenshot refreshed successfully
[[22:13:27]] [SUCCESS] Screenshot refreshed
[[22:13:27]] [INFO] Refreshing screenshot...
[[22:13:25]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[22:13:25]] [SUCCESS] Screenshot refreshed successfully
[[22:13:25]] [SUCCESS] Screenshot refreshed successfully
[[22:13:25]] [SUCCESS] Screenshot refreshed
[[22:13:25]] [INFO] Refreshing screenshot...
[[22:13:22]] [SUCCESS] Screenshot refreshed successfully
[[22:13:22]] [SUCCESS] Screenshot refreshed successfully
[[22:13:22]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[22:13:21]] [SUCCESS] Screenshot refreshed
[[22:13:21]] [INFO] Refreshing screenshot...
[[22:13:18]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[22:13:18]] [SUCCESS] Screenshot refreshed successfully
[[22:13:18]] [SUCCESS] Screenshot refreshed successfully
[[22:13:18]] [SUCCESS] Screenshot refreshed
[[22:13:18]] [INFO] Refreshing screenshot...
[[22:13:16]] [INFO] Executing action 12/19: takeScreenshot action
[[22:13:16]] [SUCCESS] Screenshot refreshed successfully
[[22:13:16]] [SUCCESS] Screenshot refreshed successfully
[[22:13:15]] [SUCCESS] Screenshot refreshed
[[22:13:15]] [INFO] Refreshing screenshot...
[[22:13:13]] [SUCCESS] Screenshot refreshed successfully
[[22:13:13]] [SUCCESS] Screenshot refreshed successfully
[[22:13:13]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[22:13:12]] [SUCCESS] Screenshot refreshed
[[22:13:12]] [INFO] Refreshing screenshot...
[[22:13:00]] [SUCCESS] Screenshot refreshed successfully
[[22:13:00]] [SUCCESS] Screenshot refreshed successfully
[[22:12:59]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[22:12:59]] [SUCCESS] Screenshot refreshed
[[22:12:59]] [INFO] Refreshing screenshot...
[[22:12:57]] [SUCCESS] Screenshot refreshed successfully
[[22:12:57]] [SUCCESS] Screenshot refreshed successfully
[[22:12:57]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[22:12:56]] [SUCCESS] Screenshot refreshed
[[22:12:56]] [INFO] Refreshing screenshot...
[[22:12:53]] [SUCCESS] Screenshot refreshed successfully
[[22:12:53]] [SUCCESS] Screenshot refreshed successfully
[[22:12:53]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[22:12:53]] [SUCCESS] Screenshot refreshed
[[22:12:53]] [INFO] Refreshing screenshot...
[[22:12:50]] [SUCCESS] Screenshot refreshed successfully
[[22:12:50]] [SUCCESS] Screenshot refreshed successfully
[[22:12:50]] [INFO] Executing action 7/19: Wait for 1 ms
[[22:12:50]] [SUCCESS] Screenshot refreshed
[[22:12:50]] [INFO] Refreshing screenshot...
[[22:12:48]] [SUCCESS] Screenshot refreshed successfully
[[22:12:48]] [SUCCESS] Screenshot refreshed successfully
[[22:12:48]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[22:12:47]] [SUCCESS] Screenshot refreshed
[[22:12:47]] [INFO] Refreshing screenshot...
[[22:12:44]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[22:12:44]] [SUCCESS] Screenshot refreshed successfully
[[22:12:44]] [SUCCESS] Screenshot refreshed successfully
[[22:12:44]] [SUCCESS] Screenshot refreshed
[[22:12:44]] [INFO] Refreshing screenshot...
[[22:12:42]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[22:12:42]] [SUCCESS] Screenshot refreshed successfully
[[22:12:42]] [SUCCESS] Screenshot refreshed successfully
[[22:12:42]] [SUCCESS] Screenshot refreshed
[[22:12:42]] [INFO] Refreshing screenshot...
[[22:12:39]] [SUCCESS] Screenshot refreshed successfully
[[22:12:39]] [SUCCESS] Screenshot refreshed successfully
[[22:12:39]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[22:12:39]] [SUCCESS] Screenshot refreshed
[[22:12:39]] [INFO] Refreshing screenshot...
[[22:12:37]] [SUCCESS] Screenshot refreshed successfully
[[22:12:37]] [SUCCESS] Screenshot refreshed successfully
[[22:12:37]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[22:12:36]] [SUCCESS] Screenshot refreshed
[[22:12:36]] [INFO] Refreshing screenshot...
[[22:12:33]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[22:12:33]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[22:12:33]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[22:12:33]] [INFO] Clearing screenshots from database before execution...
[[22:12:33]] [SUCCESS] All screenshots deleted successfully
[[22:12:33]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[22:12:33]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_221233/screenshots
[[22:12:33]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_221233
[[22:12:33]] [SUCCESS] Report directory initialized successfully
[[22:12:33]] [INFO] Initializing report directory and screenshots folder...
[[22:12:32]] [SUCCESS] Screenshot refreshed successfully
[[22:12:32]] [SUCCESS] All screenshots deleted successfully
[[22:12:32]] [INFO] All actions cleared
[[22:12:32]] [INFO] Cleaning up screenshots...
[[22:12:31]] [SUCCESS] Screenshot refreshed
[[22:12:31]] [INFO] Refreshing screenshot...
[[22:12:30]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[22:12:30]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[22:12:27]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[22:12:20]] [SUCCESS] Found 1 device(s)
[[22:12:19]] [INFO] Refreshing device list...
