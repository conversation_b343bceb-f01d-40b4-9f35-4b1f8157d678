Action Log - 2025-06-16 21:10:27
================================================================================

[[21:10:26]] [INFO] Generating execution report...
[[21:10:26]] [SUCCESS] All tests passed successfully!
[[21:10:26]] [SUCCESS] Screenshot refreshed
[[21:10:26]] [INFO] Refreshing screenshot...
[[21:10:24]] [SUCCESS] Screenshot refreshed successfully
[[21:10:24]] [SUCCESS] Screenshot refreshed successfully
[[21:10:24]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[21:10:23]] [SUCCESS] Screenshot refreshed
[[21:10:23]] [INFO] Refreshing screenshot...
[[21:10:21]] [SUCCESS] Screenshot refreshed successfully
[[21:10:21]] [SUCCESS] Screenshot refreshed successfully
[[21:10:20]] [INFO] Executing action 18/19: takeScreenshot action
[[21:10:20]] [SUCCESS] Screenshot refreshed
[[21:10:20]] [INFO] Refreshing screenshot...
[[21:10:16]] [SUCCESS] Screenshot refreshed successfully
[[21:10:16]] [SUCCESS] Screenshot refreshed successfully
[[21:10:16]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[21:10:16]] [SUCCESS] Screenshot refreshed
[[21:10:16]] [INFO] Refreshing screenshot...
[[21:10:13]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:10:13]] [SUCCESS] Screenshot refreshed successfully
[[21:10:13]] [SUCCESS] Screenshot refreshed successfully
[[21:10:12]] [SUCCESS] Screenshot refreshed
[[21:10:12]] [INFO] Refreshing screenshot...
[[21:10:11]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:10:11]] [SUCCESS] Screenshot refreshed successfully
[[21:10:11]] [SUCCESS] Screenshot refreshed successfully
[[21:10:10]] [SUCCESS] Screenshot refreshed
[[21:10:10]] [INFO] Refreshing screenshot...
[[21:10:08]] [SUCCESS] Screenshot refreshed successfully
[[21:10:08]] [SUCCESS] Screenshot refreshed successfully
[[21:10:08]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:10:07]] [SUCCESS] Screenshot refreshed
[[21:10:07]] [INFO] Refreshing screenshot...
[[21:10:04]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:10:04]] [SUCCESS] Screenshot refreshed successfully
[[21:10:04]] [SUCCESS] Screenshot refreshed successfully
[[21:10:03]] [SUCCESS] Screenshot refreshed
[[21:10:03]] [INFO] Refreshing screenshot...
[[21:10:02]] [INFO] Executing action 12/19: takeScreenshot action
[[21:10:01]] [SUCCESS] Screenshot refreshed successfully
[[21:10:01]] [SUCCESS] Screenshot refreshed successfully
[[21:10:01]] [SUCCESS] Screenshot refreshed
[[21:10:01]] [INFO] Refreshing screenshot...
[[21:09:58]] [SUCCESS] Screenshot refreshed successfully
[[21:09:58]] [SUCCESS] Screenshot refreshed successfully
[[21:09:58]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:09:58]] [SUCCESS] Screenshot refreshed
[[21:09:58]] [INFO] Refreshing screenshot...
[[21:09:46]] [SUCCESS] Screenshot refreshed successfully
[[21:09:46]] [SUCCESS] Screenshot refreshed successfully
[[21:09:45]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[21:09:45]] [SUCCESS] Screenshot refreshed
[[21:09:45]] [INFO] Refreshing screenshot...
[[21:09:43]] [SUCCESS] Screenshot refreshed successfully
[[21:09:43]] [SUCCESS] Screenshot refreshed successfully
[[21:09:43]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[21:09:42]] [SUCCESS] Screenshot refreshed
[[21:09:42]] [INFO] Refreshing screenshot...
[[21:09:39]] [SUCCESS] Screenshot refreshed successfully
[[21:09:39]] [SUCCESS] Screenshot refreshed successfully
[[21:09:39]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[21:09:38]] [SUCCESS] Screenshot refreshed
[[21:09:38]] [INFO] Refreshing screenshot...
[[21:09:36]] [SUCCESS] Screenshot refreshed successfully
[[21:09:36]] [SUCCESS] Screenshot refreshed successfully
[[21:09:36]] [INFO] Executing action 7/19: Wait for 1 ms
[[21:09:35]] [SUCCESS] Screenshot refreshed
[[21:09:35]] [INFO] Refreshing screenshot...
[[21:09:34]] [SUCCESS] Screenshot refreshed successfully
[[21:09:34]] [SUCCESS] Screenshot refreshed successfully
[[21:09:34]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[21:09:33]] [SUCCESS] Screenshot refreshed
[[21:09:33]] [INFO] Refreshing screenshot...
[[21:09:30]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:09:30]] [SUCCESS] Screenshot refreshed successfully
[[21:09:30]] [SUCCESS] Screenshot refreshed successfully
[[21:09:29]] [SUCCESS] Screenshot refreshed
[[21:09:29]] [INFO] Refreshing screenshot...
[[21:09:28]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[21:09:28]] [SUCCESS] Screenshot refreshed successfully
[[21:09:28]] [SUCCESS] Screenshot refreshed successfully
[[21:09:28]] [SUCCESS] Screenshot refreshed
[[21:09:28]] [INFO] Refreshing screenshot...
[[21:09:25]] [SUCCESS] Screenshot refreshed successfully
[[21:09:25]] [SUCCESS] Screenshot refreshed successfully
[[21:09:25]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:09:24]] [SUCCESS] Screenshot refreshed
[[21:09:24]] [INFO] Refreshing screenshot...
[[21:09:23]] [SUCCESS] Screenshot refreshed successfully
[[21:09:23]] [SUCCESS] Screenshot refreshed successfully
[[21:09:23]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[21:09:22]] [SUCCESS] Screenshot refreshed
[[21:09:22]] [INFO] Refreshing screenshot...
[[21:09:19]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[21:09:19]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[21:09:19]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[21:09:19]] [INFO] Clearing screenshots from database before execution...
[[21:09:19]] [SUCCESS] All screenshots deleted successfully
[[21:09:19]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:09:19]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_210919/screenshots
[[21:09:19]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_210919
[[21:09:19]] [SUCCESS] Report directory initialized successfully
[[21:09:19]] [INFO] Initializing report directory and screenshots folder...
[[21:09:18]] [SUCCESS] All screenshots deleted successfully
[[21:09:18]] [INFO] All actions cleared
[[21:09:18]] [INFO] Cleaning up screenshots...
[[21:09:15]] [SUCCESS] Screenshot refreshed successfully
[[21:09:14]] [SUCCESS] Screenshot refreshed
[[21:09:14]] [INFO] Refreshing screenshot...
[[21:09:13]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:09:13]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:09:10]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:09:08]] [SUCCESS] Found 1 device(s)
[[21:09:07]] [INFO] Refreshing device list...
