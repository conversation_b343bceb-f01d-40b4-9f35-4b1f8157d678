Action Log - 2025-06-16 21:37:51
================================================================================

[[21:37:51]] [INFO] Generating execution report...
[[21:37:51]] [SUCCESS] All tests passed successfully!
[[21:37:50]] [SUCCESS] Screenshot refreshed
[[21:37:50]] [INFO] Refreshing screenshot...
[[21:37:49]] [SUCCESS] Screenshot refreshed successfully
[[21:37:49]] [SUCCESS] Screenshot refreshed successfully
[[21:37:48]] [INFO] Executing action 19/19: Add Log: App is closed (with screenshot)
[[21:37:48]] [SUCCESS] Screenshot refreshed
[[21:37:48]] [INFO] Refreshing screenshot...
[[21:37:45]] [SUCCESS] Screenshot refreshed successfully
[[21:37:45]] [SUCCESS] Screenshot refreshed successfully
[[21:37:44]] [INFO] Executing action 18/19: takeScreenshot action
[[21:37:44]] [SUCCESS] Screenshot refreshed
[[21:37:44]] [INFO] Refreshing screenshot...
[[21:37:41]] [SUCCESS] Screenshot refreshed successfully
[[21:37:41]] [SUCCESS] Screenshot refreshed successfully
[[21:37:41]] [INFO] Executing action 17/19: Terminate app: com.apple.Health
[[21:37:40]] [SUCCESS] Screenshot refreshed
[[21:37:40]] [INFO] Refreshing screenshot...
[[21:37:37]] [INFO] Executing action 16/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:37:37]] [SUCCESS] Screenshot refreshed successfully
[[21:37:37]] [SUCCESS] Screenshot refreshed successfully
[[21:37:37]] [SUCCESS] Screenshot refreshed
[[21:37:37]] [INFO] Refreshing screenshot...
[[21:37:35]] [INFO] Executing action 15/19: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:37:35]] [SUCCESS] Screenshot refreshed successfully
[[21:37:35]] [SUCCESS] Screenshot refreshed successfully
[[21:37:35]] [SUCCESS] Screenshot refreshed
[[21:37:35]] [INFO] Refreshing screenshot...
[[21:37:32]] [SUCCESS] Screenshot refreshed successfully
[[21:37:32]] [SUCCESS] Screenshot refreshed successfully
[[21:37:32]] [INFO] Executing action 14/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:37:31]] [SUCCESS] Screenshot refreshed
[[21:37:31]] [INFO] Refreshing screenshot...
[[21:37:28]] [INFO] Executing action 13/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:37:28]] [SUCCESS] Screenshot refreshed successfully
[[21:37:28]] [SUCCESS] Screenshot refreshed successfully
[[21:37:28]] [SUCCESS] Screenshot refreshed
[[21:37:28]] [INFO] Refreshing screenshot...
[[21:37:26]] [INFO] Executing action 12/19: takeScreenshot action
[[21:37:26]] [SUCCESS] Screenshot refreshed successfully
[[21:37:26]] [SUCCESS] Screenshot refreshed successfully
[[21:37:25]] [SUCCESS] Screenshot refreshed
[[21:37:25]] [INFO] Refreshing screenshot...
[[21:37:23]] [SUCCESS] Screenshot refreshed successfully
[[21:37:23]] [SUCCESS] Screenshot refreshed successfully
[[21:37:23]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:37:22]] [SUCCESS] Screenshot refreshed
[[21:37:22]] [INFO] Refreshing screenshot...
[[21:37:10]] [SUCCESS] Screenshot refreshed successfully
[[21:37:10]] [SUCCESS] Screenshot refreshed successfully
[[21:37:09]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[21:37:09]] [SUCCESS] Screenshot refreshed
[[21:37:09]] [INFO] Refreshing screenshot...
[[21:37:07]] [SUCCESS] Screenshot refreshed successfully
[[21:37:07]] [SUCCESS] Screenshot refreshed successfully
[[21:37:07]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[21:37:06]] [SUCCESS] Screenshot refreshed
[[21:37:06]] [INFO] Refreshing screenshot...
[[21:37:03]] [SUCCESS] Screenshot refreshed successfully
[[21:37:03]] [SUCCESS] Screenshot refreshed successfully
[[21:37:03]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[21:37:03]] [SUCCESS] Screenshot refreshed
[[21:37:03]] [INFO] Refreshing screenshot...
[[21:37:00]] [SUCCESS] Screenshot refreshed successfully
[[21:37:00]] [SUCCESS] Screenshot refreshed successfully
[[21:37:00]] [INFO] Executing action 7/19: Wait for 1 ms
[[21:37:00]] [SUCCESS] Screenshot refreshed
[[21:37:00]] [INFO] Refreshing screenshot...
[[21:36:58]] [SUCCESS] Screenshot refreshed successfully
[[21:36:58]] [SUCCESS] Screenshot refreshed successfully
[[21:36:58]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[21:36:57]] [SUCCESS] Screenshot refreshed
[[21:36:57]] [INFO] Refreshing screenshot...
[[21:36:54]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:36:54]] [SUCCESS] Screenshot refreshed successfully
[[21:36:54]] [SUCCESS] Screenshot refreshed successfully
[[21:36:54]] [SUCCESS] Screenshot refreshed
[[21:36:54]] [INFO] Refreshing screenshot...
[[21:36:52]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[21:36:52]] [SUCCESS] Screenshot refreshed successfully
[[21:36:52]] [SUCCESS] Screenshot refreshed successfully
[[21:36:52]] [SUCCESS] Screenshot refreshed
[[21:36:52]] [INFO] Refreshing screenshot...
[[21:36:49]] [SUCCESS] Screenshot refreshed successfully
[[21:36:49]] [SUCCESS] Screenshot refreshed successfully
[[21:36:49]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:36:49]] [SUCCESS] Screenshot refreshed
[[21:36:49]] [INFO] Refreshing screenshot...
[[21:36:47]] [SUCCESS] Screenshot refreshed successfully
[[21:36:47]] [SUCCESS] Screenshot refreshed successfully
[[21:36:47]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[21:36:46]] [SUCCESS] Screenshot refreshed
[[21:36:46]] [INFO] Refreshing screenshot...
[[21:36:43]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[21:36:43]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[21:36:43]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[21:36:43]] [INFO] Clearing screenshots from database before execution...
[[21:36:43]] [SUCCESS] All screenshots deleted successfully
[[21:36:43]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:36:43]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_213643/screenshots
[[21:36:43]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_213643
[[21:36:43]] [SUCCESS] Report directory initialized successfully
[[21:36:43]] [INFO] Initializing report directory and screenshots folder...
[[21:36:41]] [SUCCESS] All screenshots deleted successfully
[[21:36:41]] [INFO] All actions cleared
[[21:36:41]] [INFO] Cleaning up screenshots...
[[21:36:35]] [SUCCESS] Screenshot refreshed successfully
[[21:36:34]] [SUCCESS] Screenshot refreshed
[[21:36:34]] [INFO] Refreshing screenshot...
[[21:36:33]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:36:33]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:36:32]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:36:25]] [SUCCESS] Found 1 device(s)
[[21:36:24]] [INFO] Refreshing device list...
