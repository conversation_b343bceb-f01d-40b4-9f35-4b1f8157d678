Action Log - 2025-06-16 21:32:11
================================================================================

[[21:32:11]] [INFO] Generating execution report...
[[21:32:11]] [WARNING] 1 test failed.
[[21:32:11]] [INFO] Skipping remaining steps in failed test case (moving from action 12 to next test case at 19)
[[21:32:11]] [ERROR] Action 12 failed: Screenshot action failed: No module named 'app.app'; 'app' is not a package
[[21:32:09]] [INFO] Executing action 12/19: takeScreenshot action
[[21:32:09]] [SUCCESS] Screenshot refreshed successfully
[[21:32:09]] [SUCCESS] Screenshot refreshed successfully
[[21:32:09]] [SUCCESS] Screenshot refreshed
[[21:32:09]] [INFO] Refreshing screenshot...
[[21:32:06]] [SUCCESS] Screenshot refreshed successfully
[[21:32:06]] [SUCCESS] Screenshot refreshed successfully
[[21:32:06]] [INFO] Executing action 11/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:32:06]] [SUCCESS] Screenshot refreshed
[[21:32:06]] [INFO] Refreshing screenshot...
[[21:31:54]] [SUCCESS] Screenshot refreshed successfully
[[21:31:54]] [SUCCESS] Screenshot refreshed successfully
[[21:31:53]] [INFO] Executing action 10/19: Launch app: com.apple.Health
[[21:31:53]] [SUCCESS] Screenshot refreshed
[[21:31:53]] [INFO] Refreshing screenshot...
[[21:31:51]] [SUCCESS] Screenshot refreshed successfully
[[21:31:51]] [SUCCESS] Screenshot refreshed successfully
[[21:31:50]] [INFO] Executing action 9/19: Add Log: App is closed (with screenshot)
[[21:31:50]] [SUCCESS] Screenshot refreshed
[[21:31:50]] [INFO] Refreshing screenshot...
[[21:31:47]] [SUCCESS] Screenshot refreshed successfully
[[21:31:47]] [SUCCESS] Screenshot refreshed successfully
[[21:31:47]] [INFO] Executing action 8/19: Terminate app: com.apple.Health
[[21:31:46]] [SUCCESS] Screenshot refreshed
[[21:31:46]] [INFO] Refreshing screenshot...
[[21:31:44]] [SUCCESS] Screenshot refreshed successfully
[[21:31:44]] [SUCCESS] Screenshot refreshed successfully
[[21:31:44]] [INFO] Executing action 7/19: Wait for 1 ms
[[21:31:43]] [SUCCESS] Screenshot refreshed
[[21:31:43]] [INFO] Refreshing screenshot...
[[21:31:42]] [SUCCESS] Screenshot refreshed successfully
[[21:31:42]] [SUCCESS] Screenshot refreshed successfully
[[21:31:42]] [INFO] Executing action 6/19: Add Log: Done link is clicked (with screenshot)
[[21:31:41]] [SUCCESS] Screenshot refreshed
[[21:31:41]] [INFO] Refreshing screenshot...
[[21:31:38]] [INFO] Executing action 5/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:31:38]] [SUCCESS] Screenshot refreshed successfully
[[21:31:38]] [SUCCESS] Screenshot refreshed successfully
[[21:31:37]] [SUCCESS] Screenshot refreshed
[[21:31:37]] [INFO] Refreshing screenshot...
[[21:31:36]] [INFO] Executing action 4/19: Add Log: Edit link is clicked (with screenshot)
[[21:31:36]] [SUCCESS] Screenshot refreshed successfully
[[21:31:36]] [SUCCESS] Screenshot refreshed successfully
[[21:31:35]] [SUCCESS] Screenshot refreshed
[[21:31:35]] [INFO] Refreshing screenshot...
[[21:31:33]] [SUCCESS] Screenshot refreshed successfully
[[21:31:33]] [SUCCESS] Screenshot refreshed successfully
[[21:31:33]] [INFO] Executing action 3/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:31:32]] [SUCCESS] Screenshot refreshed
[[21:31:32]] [INFO] Refreshing screenshot...
[[21:31:31]] [SUCCESS] Screenshot refreshed successfully
[[21:31:31]] [SUCCESS] Screenshot refreshed successfully
[[21:31:31]] [INFO] Executing action 2/19: Add Log: Launched App Successfully (with screenshot)
[[21:31:30]] [SUCCESS] Screenshot refreshed
[[21:31:30]] [INFO] Refreshing screenshot...
[[21:31:27]] [INFO] Executing action 1/19: Launch app: com.apple.Health
[[21:31:27]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[21:31:27]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[21:31:27]] [INFO] Clearing screenshots from database before execution...
[[21:31:27]] [SUCCESS] All screenshots deleted successfully
[[21:31:27]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:31:27]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_213127/screenshots
[[21:31:27]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_213127
[[21:31:27]] [SUCCESS] Report directory initialized successfully
[[21:31:27]] [INFO] Initializing report directory and screenshots folder...
[[21:31:26]] [SUCCESS] All screenshots deleted successfully
[[21:31:26]] [INFO] All actions cleared
[[21:31:26]] [INFO] Cleaning up screenshots...
[[21:31:21]] [SUCCESS] Screenshot refreshed successfully
[[21:31:20]] [SUCCESS] Screenshot refreshed
[[21:31:20]] [INFO] Refreshing screenshot...
[[21:31:19]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:31:19]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:31:17]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:31:15]] [SUCCESS] Found 1 device(s)
[[21:31:14]] [INFO] Refreshing device list...
