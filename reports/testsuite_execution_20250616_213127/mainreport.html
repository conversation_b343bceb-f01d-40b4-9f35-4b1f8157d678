<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/16/2025, 9:32:11 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 16/06/2025, 21:32:11
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1382ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="Successful.png" data-action-id="Successful" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Launched App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: Successful">Successful</span>
                            </div>
                            <span class="test-step-duration">9ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1236ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Edit link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">16ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1640ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Done link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">11ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1010ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="rxDTLvtHmR.png" data-action-id="rxDTLvtHmR" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: rxDTLvtHmR">rxDTLvtHmR</span>
                            </div>
                            <span class="test-step-duration">1052ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">13ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="10 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #2 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            10 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1204ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1192ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="failed"
                            data-screenshot="takeScreen.png" data-action-id="takeScreen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                takeScreenshot action <span class="action-id-badge" title="Action ID: takeScreen">takeScreen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="successful.png" data-action-id="successful" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: Clicked on Edit link successfully (with screenshot) <span class="action-id-badge" title="Action ID: successful">successful</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-1-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="takeScreen.png" data-action-id="takeScreen" onclick="showStepDetails('step-1-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                takeScreenshot action <span class="action-id-badge" title="Action ID: takeScreen">takeScreen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="unknown"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-1-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 16/06/2025, 21:32:11","testCases":[{"name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1382ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"Add Log: Launched App Successfully (with screenshot)","status":"passed","duration":"9ms","action_id":"Successful","screenshot_filename":"Successful.png","report_screenshot":"Successful.png","resolved_screenshot":"screenshots/Successful.png","action_id_screenshot":"screenshots/Successful.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1236ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Edit link is clicked (with screenshot)","status":"passed","duration":"16ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1640ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Done link is clicked (with screenshot)","status":"passed","duration":"11ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Wait for 1 ms","status":"passed","duration":"1010ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1052ms","action_id":"rxDTLvtHmR","screenshot_filename":"rxDTLvtHmR.png","report_screenshot":"rxDTLvtHmR.png","resolved_screenshot":"screenshots/rxDTLvtHmR.png","clean_action_id":"rxDTLvtHmR","prefixed_action_id":"al_rxDTLvtHmR","action_id_screenshot":"screenshots/rxDTLvtHmR.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"13ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            10 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1204ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1192ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"takeScreenshot action","status":"failed","duration":"0ms","action_id":"takeScreen","screenshot_filename":"takeScreen.png","report_screenshot":"takeScreen.png","resolved_screenshot":"screenshots/takeScreen.png","action_id_screenshot":"screenshots/takeScreen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Clicked on Edit link successfully (with screenshot)","status":"unknown","duration":"0ms","action_id":"successful","screenshot_filename":"successful.png","report_screenshot":"successful.png","resolved_screenshot":"screenshots/successful.png","action_id_screenshot":"screenshots/successful.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"unknown","duration":"0ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"takeScreenshot action","status":"unknown","duration":"0ms","action_id":"takeScreen","screenshot_filename":"takeScreen.png","report_screenshot":"takeScreen.png","resolved_screenshot":"screenshots/takeScreen.png","action_id_screenshot":"screenshots/takeScreen.png"},{"name":"Add Log: App is closed (with screenshot)","status":"unknown","duration":"0ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]}],"passed":1,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["4kBvNvFi5i.png","E5An5BbVuK.png","KfOSdvcOkk.png","KzjZOcsLuC.png","SaJtvXOGlT.png","To6rgFtm9R.png","ag29wsBP24.png","ee5KkVz90e.png","jE4eZaRFK6.png","mmT4QEfEZD.png","rxDTLvtHmR.png","yvWe991wY2.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>