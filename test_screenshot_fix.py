#!/usr/bin/env python3
"""
Test script to verify that takeScreenshot actions save screenshots directly to report folders
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# Add the app directory to the path
sys.path.append('app')

def test_screenshot_action():
    """Test the takeScreenshot action with the fixes"""
    
    # Create a temporary report directory structure
    temp_dir = tempfile.mkdtemp()
    print(f"Created temporary test directory: {temp_dir}")
    
    try:
        # Create report structure like the real app
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_dir = os.path.join(temp_dir, f"testsuite_execution_{timestamp}")
        screenshots_dir = os.path.join(report_dir, "screenshots")
        
        os.makedirs(screenshots_dir, exist_ok=True)
        print(f"Created report directory: {report_dir}")
        print(f"Created screenshots directory: {screenshots_dir}")
        
        # Mock the current_screenshots_dir global variable
        import app.app as app_module
        original_screenshots_dir = getattr(app_module, 'current_screenshots_dir', None)
        app_module.current_screenshots_dir = screenshots_dir
        
        # Create a mock controller for testing
        class MockController:
            def take_screenshot(self, filename=None, action_id=None):
                """Mock screenshot method that creates a dummy file"""
                if filename:
                    # Create a dummy screenshot file
                    os.makedirs(os.path.dirname(filename), exist_ok=True)
                    with open(filename, 'w') as f:
                        f.write("Mock screenshot content")
                    return {
                        'status': 'success',
                        'path': filename,
                        'message': 'Mock screenshot taken'
                    }
                else:
                    # Fallback path
                    fallback_path = os.path.join(screenshots_dir, f"{action_id or 'test'}.png")
                    with open(fallback_path, 'w') as f:
                        f.write("Mock screenshot content")
                    return {
                        'status': 'success',
                        'path': fallback_path,
                        'message': 'Mock screenshot taken'
                    }
        
        # Import and test the takeScreenshot action
        from actions.take_screenshot_action import TakeScreenshotAction
        
        # Create the action instance
        action = TakeScreenshotAction(MockController())
        
        # Test parameters
        test_params = {
            'action_id': 'test123',
            'screenshot_name': 'custom_test_screenshot'
        }
        
        print(f"Testing takeScreenshot action with params: {test_params}")
        
        # Execute the action
        result = action.execute(test_params)
        
        print(f"Action result: {result}")
        
        # Check if screenshots were created in the report directory
        expected_action_id_file = os.path.join(screenshots_dir, "test123.png")
        expected_custom_file = os.path.join(screenshots_dir, "custom_test_screenshot.png")
        
        print(f"Checking for action_id screenshot: {expected_action_id_file}")
        print(f"File exists: {os.path.exists(expected_action_id_file)}")
        
        print(f"Checking for custom screenshot: {expected_custom_file}")
        print(f"File exists: {os.path.exists(expected_custom_file)}")
        
        # List all files in the screenshots directory
        print(f"Files in screenshots directory:")
        for file in os.listdir(screenshots_dir):
            file_path = os.path.join(screenshots_dir, file)
            print(f"  - {file} (size: {os.path.getsize(file_path)} bytes)")
        
        # Restore the original screenshots directory
        app_module.current_screenshots_dir = original_screenshots_dir
        
        # Test passed if both files exist
        success = os.path.exists(expected_action_id_file) and os.path.exists(expected_custom_file)
        print(f"Test {'PASSED' if success else 'FAILED'}")
        
        return success
        
    finally:
        # Clean up
        shutil.rmtree(temp_dir)
        print(f"Cleaned up temporary directory: {temp_dir}")

if __name__ == "__main__":
    print("Testing takeScreenshot action fixes...")
    success = test_screenshot_action()
    if success:
        print("✅ All tests passed! Screenshots are being saved to report directories.")
    else:
        print("❌ Tests failed! Screenshots are not being saved correctly.")
    sys.exit(0 if success else 1)
